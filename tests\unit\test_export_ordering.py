"""Unit tests for export ordering functionality."""

import pytest
import tempfile
import os
from unittest.mock import AsyncMock, patch, MagicMock

from magic_gateway.db.clickhouse_handler import Click<PERSON>ouse<PERSON>andler


class TestExportOrdering:
    """Test cases for export ordering functionality."""

    @pytest.mark.asyncio
    async def test_export_table_to_parquet_includes_order_by(self):
        """Test that export_table_to_parquet includes ORDER BY clause for consistent ordering."""
        # Arrange
        table_name = "kpi_results.result_table_123"
        query_id = "test_query_123"

        # Mock the export_to_parquet_file method to capture the query
        captured_query = None

        async def mock_export_to_parquet_file(
            query, output_file_path, query_id, allow_write
        ):
            nonlocal captured_query
            captured_query = query
            return (output_file_path, 100)  # Return mock file path and row count

        # Act
        with patch.object(
            ClickHouseHandler,
            "export_to_parquet_file",
            side_effect=mock_export_to_parquet_file,
        ):
            result = await ClickHouseHandler.export_table_to_parquet(
                table_name=table_name, query_id=query_id
            )

        # Assert
        assert captured_query is not None, "Query should have been captured"
        assert "ORDER BY tuple(*)" in captured_query, (
            "Query should include ORDER BY tuple(*) for consistent ordering"
        )
        assert f"SELECT * FROM {table_name}" in captured_query, (
            "Query should select from the correct table"
        )

        # Verify the complete expected query structure
        expected_query = f"SELECT * FROM {table_name} ORDER BY tuple(*)"
        assert captured_query == expected_query, (
            f"Expected query: {expected_query}, but got: {captured_query}"
        )

        # Verify return values
        file_path, row_count = result
        assert row_count == 100, "Should return the mocked row count"

    @pytest.mark.asyncio
    async def test_export_table_to_parquet_query_structure(self):
        """Test that the export query has the correct structure for deterministic ordering."""
        # Arrange
        table_name = "test_database.test_table"

        # Mock the export_to_parquet_file method
        captured_queries = []

        async def mock_export_to_parquet_file(
            query, output_file_path, query_id, allow_write
        ):
            captured_queries.append(query)
            return (output_file_path, 50)

        # Act
        with patch.object(
            ClickHouseHandler,
            "export_to_parquet_file",
            side_effect=mock_export_to_parquet_file,
        ):
            await ClickHouseHandler.export_table_to_parquet(table_name=table_name)

        # Assert
        assert len(captured_queries) == 1, "Should have captured exactly one query"
        query = captured_queries[0]

        # Verify query components
        assert query.startswith("SELECT * FROM"), (
            "Query should start with SELECT * FROM"
        )
        assert table_name in query, "Query should reference the correct table"
        assert query.endswith("ORDER BY tuple(*)"), (
            "Query should end with ORDER BY tuple(*)"
        )

        # Verify the complete query structure
        expected_query = f"SELECT * FROM {table_name} ORDER BY tuple(*)"
        assert query == expected_query, f"Expected: {expected_query}, got: {query}"

    @pytest.mark.asyncio
    async def test_export_table_to_parquet_with_special_table_names(self):
        """Test export with various table name formats."""
        # Arrange
        test_cases = [
            "database.table",
            "database.schema.table",
            "kpi_results.result_table_12345",
            "metadata.results_metadata",
        ]

        captured_queries = []

        async def mock_export_to_parquet_file(
            query, output_file_path, query_id, allow_write
        ):
            captured_queries.append(query)
            return (output_file_path, 10)

        # Act & Assert
        with patch.object(
            ClickHouseHandler,
            "export_to_parquet_file",
            side_effect=mock_export_to_parquet_file,
        ):
            for table_name in test_cases:
                await ClickHouseHandler.export_table_to_parquet(table_name=table_name)

                # Verify the query for this table
                query = captured_queries[-1]  # Get the last captured query
                expected_query = f"SELECT * FROM {table_name} ORDER BY tuple(*)"
                assert query == expected_query, (
                    f"For table {table_name}, expected: {expected_query}, got: {query}"
                )

    def test_order_by_tuple_explanation(self):
        """Test to document why ORDER BY tuple(*) is used."""
        # This test serves as documentation for the ORDER BY choice

        # ORDER BY tuple(*) creates a deterministic ordering using all columns
        # This ensures:
        # 1. Consistent results across multiple export operations
        # 2. No need to know the table's schema or primary key
        # 3. Works with any table structure
        # 4. Provides stable sorting for identical rows

        # The tuple(*) function in ClickHouse creates a tuple from all columns,
        # which is then used for sorting, ensuring deterministic ordering

        assert True, "This test documents the ORDER BY tuple(*) approach"

    @pytest.mark.asyncio
    async def test_export_table_to_parquet_error_handling(self):
        """Test that errors in export_to_parquet_file are properly propagated."""
        # Arrange
        table_name = "test.table"

        async def mock_export_to_parquet_file_error(
            query, output_file_path, query_id, allow_write
        ):
            raise Exception("Mock export error")

        # Act & Assert
        with patch.object(
            ClickHouseHandler,
            "export_to_parquet_file",
            side_effect=mock_export_to_parquet_file_error,
        ):
            with pytest.raises(Exception, match="Mock export error"):
                await ClickHouseHandler.export_table_to_parquet(table_name=table_name)
